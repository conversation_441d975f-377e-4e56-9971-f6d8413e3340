"""
Model utilities for TJA chart generation.
Provides model architecture components and utilities for Phase 3.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
import json
import os
from pathlib import Path

logger = logging.getLogger(__name__)


class TemporalConvBlock(nn.Module):
    """Temporal Convolutional Block with dilated convolutions."""
    
    def __init__(self, in_channels: int, out_channels: int, kernel_size: int = 3, 
                 dilation: int = 1, dropout: float = 0.1, activation: str = "relu"):
        super().__init__()
        
        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size, 
                              dilation=dilation, padding=(kernel_size-1)*dilation//2)
        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size, 
                              dilation=dilation, padding=(kernel_size-1)*dilation//2)
        
        self.norm1 = nn.BatchNorm1d(out_channels)
        self.norm2 = nn.BatchNorm1d(out_channels)
        
        self.dropout = nn.Dropout(dropout)
        
        # Activation function
        if activation == "relu":
            self.activation = nn.ReLU()
        elif activation == "gelu":
            self.activation = nn.GELU()
        else:
            self.activation = nn.ReLU()
            
        # Residual connection
        self.residual = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else nn.Identity()
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through temporal conv block."""
        residual = self.residual(x)
        
        out = self.conv1(x)
        out = self.norm1(out)
        out = self.activation(out)
        out = self.dropout(out)
        
        out = self.conv2(out)
        out = self.norm2(out)
        
        # Residual connection
        out = out + residual
        out = self.activation(out)
        
        return out


class MultiHeadAttention(nn.Module):
    """Multi-head attention mechanism for temporal modeling."""
    
    def __init__(self, embed_dim: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.head_dim = embed_dim // num_heads
        
        assert self.head_dim * num_heads == embed_dim, "embed_dim must be divisible by num_heads"
        
        self.q_proj = nn.Linear(embed_dim, embed_dim)
        self.k_proj = nn.Linear(embed_dim, embed_dim)
        self.v_proj = nn.Linear(embed_dim, embed_dim)
        self.out_proj = nn.Linear(embed_dim, embed_dim)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = self.head_dim ** -0.5
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Forward pass through multi-head attention."""
        batch_size, seq_len, embed_dim = x.shape
        
        # Project to Q, K, V
        q = self.q_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = self.k_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = self.v_proj(x).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # Scaled dot-product attention
        scores = torch.matmul(q, k.transpose(-2, -1)) * self.scale
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
            
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # Apply attention to values
        out = torch.matmul(attn_weights, v)
        out = out.transpose(1, 2).contiguous().view(batch_size, seq_len, embed_dim)
        
        # Output projection
        out = self.out_proj(out)
        
        return out


class TJAEncoder(nn.Module):
    """Encoder for audio features using Temporal Convolutional Networks."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        encoder_config = config['encoder']
        input_dim = config['input_features']['feature_dim']
        channels = encoder_config['channels']
        
        # Input projection
        self.input_proj = nn.Conv1d(input_dim, channels[0], 1)
        
        # TCN layers
        self.tcn_layers = nn.ModuleList()
        for i in range(len(channels) - 1):
            dilation = encoder_config['dilation_base'] ** i
            self.tcn_layers.append(
                TemporalConvBlock(
                    channels[i], channels[i+1],
                    kernel_size=encoder_config['kernel_size'],
                    dilation=dilation,
                    dropout=encoder_config['dropout'],
                    activation=encoder_config['activation']
                )
            )
        
        self.output_dim = channels[-1]
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through encoder."""
        # x shape: (batch, time, features) -> (batch, features, time)
        x = x.transpose(1, 2)
        
        # Input projection
        x = self.input_proj(x)
        
        # TCN layers
        for layer in self.tcn_layers:
            x = layer(x)
            
        # Back to (batch, time, features)
        x = x.transpose(1, 2)
        
        return x


class TJADecoder(nn.Module):
    """Decoder for TJA chart generation using Temporal Convolutional Networks."""

    def __init__(self, config: Dict[str, Any]):
        super().__init__()

        decoder_config = config['decoder']
        attention_config = config['attention']
        output_config = config['output']
        input_config = config['input_features']

        input_dim = attention_config['embed_dim']
        channels = decoder_config['channels']

        # Store sequence length information
        self.input_seq_len = input_config['sequence_length']
        self.output_seq_len = output_config['sequence_length']

        # Input projection
        self.input_proj = nn.Conv1d(input_dim, channels[0], 1)

        # TCN layers
        self.tcn_layers = nn.ModuleList()
        for i in range(len(channels) - 1):
            dilation = decoder_config['dilation_base'] ** i
            self.tcn_layers.append(
                TemporalConvBlock(
                    channels[i], channels[i+1],
                    kernel_size=decoder_config['kernel_size'],
                    dilation=dilation,
                    dropout=decoder_config['dropout'],
                    activation=decoder_config['activation']
                )
            )

        # Sequence length adaptation layer
        if self.input_seq_len != self.output_seq_len:
            self.seq_adapter = nn.Conv1d(channels[-1], channels[-1],
                                       kernel_size=3, stride=2, padding=1)
        else:
            self.seq_adapter = nn.Identity()

        # Output projection
        self.output_proj = nn.Conv1d(channels[-1], output_config['num_classes'], 1)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass through decoder."""
        # x shape: (batch, time, features) -> (batch, features, time)
        x = x.transpose(1, 2)

        # Input projection
        x = self.input_proj(x)

        # TCN layers
        for layer in self.tcn_layers:
            x = layer(x)

        # Adapt sequence length if needed
        x = self.seq_adapter(x)

        # Ensure exact output sequence length
        if x.size(2) != self.output_seq_len:
            x = F.interpolate(x, size=self.output_seq_len, mode='linear', align_corners=False)

        # Output projection
        x = self.output_proj(x)

        # Back to (batch, time, classes)
        x = x.transpose(1, 2)

        return x


def calculate_model_size(model: nn.Module) -> Dict[str, float]:
    """Calculate model size and parameter count."""
    param_count = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    # Estimate model size in MB (assuming float32)
    model_size_mb = param_count * 4 / (1024 * 1024)
    
    return {
        'total_parameters': param_count,
        'trainable_parameters': trainable_params,
        'model_size_mb': model_size_mb
    }


def estimate_memory_usage(model: nn.Module, batch_size: int, sequence_length: int, 
                         feature_dim: int) -> Dict[str, float]:
    """Estimate memory usage for model training and inference."""
    
    # Model parameters
    param_memory = sum(p.numel() * 4 for p in model.parameters()) / (1024**3)  # GB
    
    # Input tensor memory
    input_memory = batch_size * sequence_length * feature_dim * 4 / (1024**3)  # GB
    
    # Rough estimate for activations (varies by architecture)
    activation_memory = input_memory * 10  # Conservative estimate
    
    # Gradient memory (same as parameters for training)
    gradient_memory = param_memory
    
    # Optimizer state memory (AdamW uses ~2x parameter memory)
    optimizer_memory = param_memory * 2
    
    total_training_memory = param_memory + input_memory + activation_memory + gradient_memory + optimizer_memory
    total_inference_memory = param_memory + input_memory + activation_memory
    
    return {
        'parameter_memory_gb': param_memory,
        'input_memory_gb': input_memory,
        'activation_memory_gb': activation_memory,
        'gradient_memory_gb': gradient_memory,
        'optimizer_memory_gb': optimizer_memory,
        'total_training_memory_gb': total_training_memory,
        'total_inference_memory_gb': total_inference_memory
    }


def save_model_config(config: Dict[str, Any], output_path: str) -> None:
    """Save model configuration to JSON file."""
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Model configuration saved to: {output_path}")


def load_model_config(config_path: str) -> Dict[str, Any]:
    """Load model configuration from JSON file."""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    logger.info(f"Model configuration loaded from: {config_path}")
    return config


def validate_model_config(config: Dict[str, Any]) -> bool:
    """Validate model configuration parameters."""
    required_sections = ['model_architecture', 'memory', 'training', 'loss']

    for section in required_sections:
        if section not in config:
            logger.error(f"Missing required configuration section: {section}")
            return False

    # Validate memory constraints
    memory_config = config['memory']
    if memory_config['max_vram_usage_gb'] > 8.0:
        logger.warning("VRAM usage exceeds RTX 3070 capacity (8GB)")

    # Validate model architecture
    arch_config = config['model_architecture']
    if arch_config['output']['num_classes'] != 5:
        logger.error("Output classes must be 5 for simplified TJA format")
        return False

    logger.info("Model configuration validation passed")
    return True


def create_model_summary(model) -> str:
    """Create a human-readable model architecture summary."""

    model_info = model.get_model_info()

    summary = []
    summary.append("=" * 80)
    summary.append("TJA GENERATOR MODEL ARCHITECTURE SUMMARY")
    summary.append("=" * 80)

    # Basic information
    summary.append(f"Architecture Type: {model_info['architecture_type']}")
    summary.append(f"Input Shape: {model_info['input_shape']}")
    summary.append(f"Output Shape: {model_info['output_shape']}")
    summary.append("")

    # Model size information
    size_info = model_info['model_size']
    summary.append("MODEL SIZE:")
    summary.append(f"  Total Parameters: {size_info['total_parameters']:,}")
    summary.append(f"  Trainable Parameters: {size_info['trainable_parameters']:,}")
    summary.append(f"  Model Size: {size_info['model_size_mb']:.2f} MB")
    summary.append("")

    # Memory usage information
    memory_info = model_info['memory_usage']
    summary.append("MEMORY USAGE:")
    summary.append(f"  Training Memory: {memory_info['total_training_memory_gb']:.3f} GB")
    summary.append(f"  Inference Memory: {memory_info['total_inference_memory_gb']:.3f} GB")
    summary.append(f"  Parameter Memory: {memory_info['parameter_memory_gb']:.3f} GB")
    summary.append(f"  Gradient Memory: {memory_info['gradient_memory_gb']:.3f} GB")
    summary.append(f"  Optimizer Memory: {memory_info['optimizer_memory_gb']:.3f} GB")
    summary.append("")

    return "\n".join(summary)
