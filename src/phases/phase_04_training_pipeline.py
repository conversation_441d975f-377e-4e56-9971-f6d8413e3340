"""
Phase 4: Training Pipeline Implementation
Main training pipeline for TJA chart generation models.
"""

import torch
import torch.nn as nn
import yaml
import json
import os
import logging
import time
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

from ..phases.phase_03_model_architecture import TJ<PERSON>eneratorModel
from ..training.trainer import TJATrainer
from ..data.tja_dataset import create_data_loaders
from ..utils.path_utils import ensure_directory_exists

logger = logging.getLogger(__name__)


class TrainingPipeline:
    """Main training pipeline for Phase 4."""
    
    def __init__(self, config_path: str):
        """Initialize the training pipeline."""

        # Load configuration
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # Setup paths first (required for logging)
        self.setup_paths()

        # Setup logging (after paths are created)
        self.setup_logging()

        # Setup device
        self.setup_device()
        
        # Initialize statistics
        self.stats = {
            'training_started': None,
            'training_completed': None,
            'total_training_time': 0.0,
            'epochs_completed': 0,
            'best_validation_metrics': {},
            'final_model_path': None,
            'training_success': False
        }
        
        logger.info("Training Pipeline initialized")
    
    def setup_paths(self) -> None:
        """Setup output directory structure."""
        paths = self.config['paths']
        
        # Create output directories
        output_dirs = [
            'output_root', 'model_checkpoints', 'training_logs',
            'validation_results', 'tensorboard_logs'
        ]
        
        for dir_key in output_dirs:
            if dir_key in paths:
                # Create directory directly using os.makedirs
                os.makedirs(paths[dir_key], exist_ok=True)

        # Note: logger not yet configured, so no logging here
    
    def setup_logging(self) -> None:
        """Setup logging configuration."""
        log_dir = self.config['paths']['training_logs']
        log_file = os.path.join(log_dir, 'phase4_training.log')

        # Ensure log directory exists
        ensure_directory_exists(log_dir)

        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

        logger.info("Logging configured")
        logger.info("Output directory structure created")
    
    def setup_device(self) -> None:
        """Setup training device."""
        hardware_config = self.config['hardware']
        
        if hardware_config['device'] == 'cuda' and torch.cuda.is_available():
            self.device = torch.device(f"cuda:{hardware_config['cuda_device_id']}")
            
            # Set CUDA optimization flags
            if hardware_config['benchmark']:
                torch.backends.cudnn.benchmark = True
            
            if hardware_config['deterministic']:
                torch.backends.cudnn.deterministic = True
            
            logger.info(f"Using device: {self.device}")
            logger.info(f"GPU: {torch.cuda.get_device_name(self.device)}")
            logger.info(f"VRAM: {torch.cuda.get_device_properties(self.device).total_memory / 1e9:.1f} GB")
        else:
            self.device = torch.device('cpu')
            logger.info("Using device: CPU")
    
    def load_model(self, variant_name: str) -> TJAGeneratorModel:
        """Load model from Phase 3 configuration."""
        
        # Load model configuration
        config_path = os.path.join(
            self.config['paths']['model_configs'],
            f"{variant_name}_config.json"
        )
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Model configuration not found: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            model_config = json.load(f)
        
        # Create model
        model = TJAGeneratorModel(model_config)
        
        logger.info(f"Model loaded: {variant_name}")
        logger.info(f"Parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        return model
    
    def create_data_loaders(self) -> Tuple[torch.utils.data.DataLoader, torch.utils.data.DataLoader]:
        """Create training and validation data loaders."""
        
        logger.info("Creating data loaders...")
        
        # Set random seed for reproducible data splits
        torch.manual_seed(self.config['experiment']['random_seed'])
        
        # Create data loaders
        train_loader, val_loader = create_data_loaders(self.config)
        
        logger.info(f"Data loaders created:")
        logger.info(f"  Training batches: {len(train_loader)}")
        logger.info(f"  Validation batches: {len(val_loader)}")
        logger.info(f"  Batch size: {self.config['training']['batch_size']}")
        
        return train_loader, val_loader
    
    def train_model(self, model: nn.Module, train_loader, val_loader) -> Dict[str, Any]:
        """Train the model with the given data loaders."""
        
        logger.info("Starting model training...")
        
        # Create trainer
        trainer = TJATrainer(model, self.config, self.device)
        
        # Training configuration
        training_config = self.config['training']
        max_epochs = training_config['epochs']
        save_frequency = training_config['save_frequency']
        validation_frequency = training_config['validation_frequency']
        
        # Training loop
        training_start_time = time.time()
        self.stats['training_started'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        try:
            for epoch in range(max_epochs):
                trainer.current_epoch = epoch
                epoch_start_time = time.time()
                
                # Training phase
                logger.info(f"Starting epoch {epoch + 1}/{max_epochs}")
                train_metrics = trainer.train_epoch(train_loader)
                
                # Validation phase
                val_metrics = {}
                if epoch % validation_frequency == 0:
                    logger.info("Running validation...")
                    val_metrics = trainer.validate_epoch(val_loader)
                
                # Learning rate scheduling
                if trainer.scheduler:
                    trainer.scheduler.step()
                
                # Log metrics
                if val_metrics:
                    trainer.log_epoch_metrics(epoch, train_metrics, val_metrics)
                
                # Save checkpoint
                if epoch % save_frequency == 0 or epoch == max_epochs - 1:
                    checkpoint_path = os.path.join(
                        self.config['paths']['model_checkpoints'],
                        f"checkpoint_epoch_{epoch:03d}.pth"
                    )
                    
                    is_best = False
                    if val_metrics and trainer.primary_metric in val_metrics:
                        current_metric = val_metrics[trainer.primary_metric]
                        if current_metric > trainer.best_val_metric:
                            is_best = True
                            self.stats['best_validation_metrics'] = val_metrics.copy()
                    
                    trainer.save_checkpoint(checkpoint_path, is_best)
                    
                    if is_best:
                        self.stats['final_model_path'] = checkpoint_path.replace('.pth', '_best.pth')
                
                # Early stopping check
                if val_metrics and trainer.primary_metric in val_metrics:
                    if trainer.should_stop_early(val_metrics[trainer.primary_metric]):
                        logger.info(f"Early stopping triggered at epoch {epoch}")
                        break
                
                # Update statistics
                self.stats['epochs_completed'] = epoch + 1
                
                epoch_time = time.time() - epoch_start_time
                logger.info(f"Epoch {epoch + 1} completed in {epoch_time:.2f} seconds")
                
                # Memory cleanup
                torch.cuda.empty_cache()
        
        except KeyboardInterrupt:
            logger.info("Training interrupted by user")
        except Exception as e:
            logger.error(f"Training failed: {str(e)}")
            raise
        finally:
            # Cleanup
            trainer.cleanup()
        
        # Calculate total training time
        total_time = time.time() - training_start_time
        self.stats['total_training_time'] = total_time
        self.stats['training_completed'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.stats['training_success'] = True
        
        logger.info(f"Training completed in {total_time:.2f} seconds")
        
        # Return training results
        return {
            'train_history': trainer.train_history,
            'val_history': trainer.val_history,
            'best_val_metric': trainer.best_val_metric,
            'epochs_completed': self.stats['epochs_completed'],
            'total_time': total_time
        }
    
    def validate_training_setup(self) -> bool:
        """Validate that training setup is correct."""
        
        logger.info("Validating training setup...")
        
        # Check data availability
        audio_path = self.config['paths']['input_audio']
        if not os.path.exists(audio_path):
            logger.error(f"Audio data path not found: {audio_path}")
            return False
        
        audio_files = [f for f in os.listdir(audio_path) if f.endswith('.npy')]
        if len(audio_files) < 100:  # Minimum threshold
            logger.error(f"Insufficient audio files: {len(audio_files)}")
            return False
        
        logger.info(f"Found {len(audio_files)} audio files")
        
        # Check model configurations
        model_configs_path = self.config['paths']['model_configs']
        primary_model = self.config['training']['primary_model']
        config_file = f"{primary_model}_config.json"
        
        if not os.path.exists(os.path.join(model_configs_path, config_file)):
            logger.error(f"Model configuration not found: {config_file}")
            return False
        
        logger.info(f"Model configuration validated: {primary_model}")
        
        # Check GPU memory
        if self.device.type == 'cuda':
            total_memory = torch.cuda.get_device_properties(self.device).total_memory
            max_memory_gb = self.config['memory']['max_vram_usage_gb']
            
            if total_memory / 1e9 < max_memory_gb:
                logger.warning(f"GPU memory may be insufficient: "
                             f"{total_memory/1e9:.1f}GB < {max_memory_gb}GB")
        
        logger.info("Training setup validation passed")
        return True
    
    def run_training_pipeline(self) -> Dict[str, Any]:
        """Execute the complete training pipeline."""
        
        logger.info("=== Starting Phase 4: Training Pipeline ===")
        start_time = time.time()
        
        try:
            # Validate setup
            if not self.validate_training_setup():
                raise RuntimeError("Training setup validation failed")
            
            # Load model
            primary_model = self.config['training']['primary_model']
            model = self.load_model(primary_model)
            
            # Create data loaders
            train_loader, val_loader = self.create_data_loaders()
            
            # Train model
            training_results = self.train_model(model, train_loader, val_loader)
            
            # Generate summary report
            summary = self.generate_summary_report(training_results)
            
            total_time = time.time() - start_time
            logger.info(f"=== Phase 4 completed successfully in {total_time:.2f} seconds ===")
            
            return summary
            
        except Exception as e:
            logger.error(f"Phase 4 failed: {str(e)}")
            raise
    
    def generate_summary_report(self, training_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive summary report."""
        
        summary = {
            'phase': 'Phase 4: Training Pipeline',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'configuration': self.config,
            'statistics': self.stats,
            'training_results': training_results,
            'quality_metrics': {
                'training_success': self.stats['training_success'],
                'epochs_completed': self.stats['epochs_completed'],
                'best_validation_metrics': self.stats['best_validation_metrics'],
                'total_training_time_hours': self.stats['total_training_time'] / 3600
            }
        }
        
        # Save summary report
        summary_path = os.path.join(
            self.config['paths']['training_logs'],
            'phase4_training_summary.json'
        )
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Training summary saved to: {summary_path}")
        return summary
