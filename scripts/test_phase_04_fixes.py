"""
Test script to verify Phase 4 fixes are working correctly.
Tests the NaN loss fix, deprecation warning fix, and emoji removal.
"""

import sys
import os
import torch
import yaml
import warnings
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.phases.phase_04_training_pipeline import TrainingPipeline


def test_deprecation_fix():
    """Test that PyTorch deprecation warnings are fixed."""
    
    print("TESTING PYTORCH DEPRECATION FIX")
    print("=" * 50)
    
    # Capture warnings
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        # Load configuration
        config_path = "configs/phase_04_config.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Initialize pipeline (this should trigger the fixed imports)
        pipeline = TrainingPipeline(config_path)
        
        # Load model to test trainer initialization
        model = pipeline.load_model('standard')
        
        # Check for deprecation warnings
        deprecation_warnings = [warning for warning in w if issubclass(warning.category, FutureWarning)]
        
        if deprecation_warnings:
            print(f"[FAIL] Found {len(deprecation_warnings)} deprecation warnings:")
            for warning in deprecation_warnings:
                print(f"  - {warning.message}")
            return False
        else:
            print("[PASS] No PyTorch deprecation warnings detected")
            return True


def test_nan_protection():
    """Test that NaN loss protection is working."""
    
    print("\nTESTING NaN LOSS PROTECTION")
    print("=" * 50)
    
    try:
        # Load configuration
        config_path = "configs/phase_04_config.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Initialize pipeline
        pipeline = TrainingPipeline(config_path)
        model = pipeline.load_model('standard')
        
        # Import trainer to test loss computation
        from src.training.trainer import TJATrainer
        
        trainer = TJATrainer(model, config, pipeline.device)
        
        # Test with normal inputs
        batch_size = 2
        seq_len = 512
        num_classes = 5

        normal_outputs = torch.randn(batch_size, seq_len, num_classes).to(pipeline.device)
        normal_targets = torch.randint(0, num_classes, (batch_size, seq_len)).to(pipeline.device)
        
        normal_loss = trainer._compute_loss(normal_outputs, normal_targets)
        
        if torch.isnan(normal_loss) or torch.isinf(normal_loss):
            print("[FAIL] Normal inputs produced NaN/inf loss")
            return False
        
        print(f"[PASS] Normal loss computation: {normal_loss.item():.4f}")
        
        # Test with extreme inputs (should be clamped)
        extreme_outputs = torch.full((batch_size, seq_len, num_classes), 1000.0).to(pipeline.device)  # Very large values
        extreme_targets = torch.randint(0, num_classes, (batch_size, seq_len)).to(pipeline.device)
        
        extreme_loss = trainer._compute_loss(extreme_outputs, extreme_targets)
        
        if torch.isnan(extreme_loss) or torch.isinf(extreme_loss):
            print("[FAIL] Extreme inputs produced NaN/inf loss")
            return False
        
        print(f"[PASS] Extreme inputs handled: {extreme_loss.item():.4f}")
        
        # Test with NaN inputs (should be protected)
        nan_outputs = torch.full((batch_size, seq_len, num_classes), float('nan')).to(pipeline.device)
        nan_targets = torch.randint(0, num_classes, (batch_size, seq_len)).to(pipeline.device)
        
        nan_loss = trainer._compute_loss(nan_outputs, nan_targets)
        
        if torch.isnan(nan_loss) or torch.isinf(nan_loss):
            print("[FAIL] NaN inputs not properly protected")
            return False
        
        print(f"[PASS] NaN inputs protected: {nan_loss.item():.4f}")
        
        return True
        
    except Exception as e:
        print(f"[FAIL] NaN protection test failed: {str(e)}")
        return False


def test_configuration_stability():
    """Test that configuration changes improve training stability."""
    
    print("\nTESTING CONFIGURATION STABILITY")
    print("=" * 50)
    
    try:
        # Load configuration
        config_path = "configs/phase_04_config.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Check learning rate
        lr = config['training']['learning_rate']
        if lr > 0.0001:
            print(f"[WARN] Learning rate may be too high: {lr}")
        else:
            print(f"[PASS] Learning rate appropriate for stability: {lr}")
        
        # Check weight decay
        wd = config['training']['weight_decay']
        if wd > 0.01:
            print(f"[WARN] Weight decay may be too high: {wd}")
        else:
            print(f"[PASS] Weight decay appropriate: {wd}")
        
        # Check gradient clipping
        if 'gradient_clip_norm' in config['training']:
            clip_norm = config['training']['gradient_clip_norm']
            print(f"[PASS] Gradient clipping enabled: {clip_norm}")
        else:
            print("[WARN] Gradient clipping not configured")
            return False
        
        return True
        
    except Exception as e:
        print(f"[FAIL] Configuration stability test failed: {str(e)}")
        return False


def main():
    """Main test execution."""
    
    print("TESTING PHASE 4 FIXES")
    print("=" * 80)
    
    test_results = []
    
    # Run all tests
    test_results.append(("PyTorch Deprecation Fix", test_deprecation_fix()))
    test_results.append(("NaN Loss Protection", test_nan_protection()))
    test_results.append(("Configuration Stability", test_configuration_stability()))
    
    # Summary
    print("\n" + "=" * 80)
    print("PHASE 4 FIXES TEST SUMMARY")
    print("=" * 80)
    
    all_passed = True
    for test_name, result in test_results:
        status = "PASSED" if result else "FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("ALL FIXES VALIDATED - PHASE 4 READY FOR TRAINING")
        print("Run: python scripts/run_phase_04.py")
        return 0
    else:
        print("SOME FIXES FAILED - REVIEW ISSUES ABOVE")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
