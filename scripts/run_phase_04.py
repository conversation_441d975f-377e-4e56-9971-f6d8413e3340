"""
Phase 4 Execution Script: Training Pipeline
Runs the complete Phase 4 training process for TJA chart generation.
"""

import sys
import os
import logging
import traceback
import torch
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.phases.phase_04_training_pipeline import TrainingPipeline


def check_prerequisites():
    """Check that all prerequisites are met for Phase 4."""

    print("Checking Phase 4 prerequisites...")

    # Check Phase 3 outputs
    phase3_configs = "data/processed/phase3/model_configs"
    if not os.path.exists(phase3_configs):
        raise FileNotFoundError(f"Phase 3 model configurations not found: {phase3_configs}")

    required_configs = ["standard_config.json", "lightweight_config.json", "enhanced_config.json"]
    for config_file in required_configs:
        config_path = os.path.join(phase3_configs, config_file)
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Required model configuration missing: {config_path}")

    print("[PASS] Phase 3 model configurations found")
    
    # Check Phase 2 data
    phase2_audio = "data/processed/phase2/filtered_audio"
    if not os.path.exists(phase2_audio):
        raise FileNotFoundError(f"Phase 2 audio data not found: {phase2_audio}")
    
    audio_files = [f for f in os.listdir(phase2_audio) if f.endswith('.npy')]
    if len(audio_files) < 100:
        raise ValueError(f"Insufficient audio files for training: {len(audio_files)}")
    
    print(f"[PASS] Phase 2 audio data found: {len(audio_files)} files")

    # Check Phase 1 metadata
    phase1_metadata = "data/processed/phase1/metadata"
    if not os.path.exists(phase1_metadata):
        raise FileNotFoundError(f"Phase 1 metadata not found: {phase1_metadata}")

    metadata_files = [f for f in os.listdir(phase1_metadata) if f.endswith('.json')]
    print(f"[PASS] Phase 1 metadata found: {len(metadata_files)} files")

    # Check CUDA availability
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        print(f"[PASS] CUDA available: {gpu_name} ({gpu_memory:.1f} GB)")
    else:
        print("[WARN] CUDA not available - training will use CPU (slower)")

    print("[PASS] All prerequisites met")


def display_training_progress(summary):
    """Display training progress and results."""
    
    print("\n" + "=" * 80)
    print("PHASE 4 TRAINING SUMMARY")
    print("=" * 80)
    
    stats = summary['statistics']
    training_results = summary['training_results']
    quality_metrics = summary['quality_metrics']
    
    # Training overview
    print(f"Training Started: {stats['training_started']}")
    print(f"Training Completed: {stats['training_completed']}")
    print(f"Total Training Time: {stats['total_training_time']:.2f} seconds ({stats['total_training_time']/3600:.2f} hours)")
    print(f"Epochs Completed: {stats['epochs_completed']}")
    print(f"Training Success: {'PASS' if stats['training_success'] else 'FAIL'}")
    
    # Model performance
    if stats['best_validation_metrics']:
        print(f"\nBest Validation Metrics:")
        for metric, value in stats['best_validation_metrics'].items():
            print(f"  {metric.capitalize()}: {value:.4f}")
    
    # Training history
    if 'train_history' in training_results:
        train_hist = training_results['train_history']
        val_hist = training_results['val_history']
        
        print(f"\nFinal Training Metrics:")
        for metric in ['loss', 'accuracy', 'f1_score']:
            if metric in train_hist and train_hist[metric]:
                train_val = train_hist[metric][-1]
                val_val = val_hist[metric][-1] if metric in val_hist and val_hist[metric] else 0
                print(f"  {metric.capitalize()}: Train={train_val:.4f}, Val={val_val:.4f}")
    
    # Quality gates
    print(f"\nQuality Assessment:")
    print(f"  Training Success Rate: {'PASSED' if quality_metrics['training_success'] else 'FAILED'}")
    print(f"  Epochs Completed: {quality_metrics['epochs_completed']}")
    print(f"  Training Time: {quality_metrics['total_training_time_hours']:.2f} hours")
    
    # Model checkpoint
    if stats['final_model_path']:
        print(f"\nBest Model Saved: {stats['final_model_path']}")
    
    # Output locations
    config = summary['configuration']
    print(f"\nOutput Files:")
    print(f"  Checkpoints: {config['paths']['model_checkpoints']}")
    print(f"  Training Logs: {config['paths']['training_logs']}")
    print(f"  TensorBoard: {config['paths']['tensorboard_logs']}")


def main():
    """Main execution function for Phase 4."""
    
    print("=" * 80)
    print("TJA Chart Generation - Phase 4: Training Pipeline")
    print("=" * 80)
    
    try:
        # Check prerequisites
        check_prerequisites()
        
        # Configuration file path
        config_path = os.path.join(project_root, "configs", "phase_04_config.yaml")
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        print(f"\n📋 Loading configuration from: {config_path}")
        
        # Initialize training pipeline
        print("Initializing Training Pipeline...")
        pipeline = TrainingPipeline(config_path)

        # Run training
        print("Starting Phase 4 training execution...")
        print("This may take several hours depending on dataset size and hardware...")
        
        summary = pipeline.run_training_pipeline()
        
        # Display results
        display_training_progress(summary)
        
        # Success message
        print("\n" + "=" * 80)
        print("PHASE 4 TRAINING COMPLETED SUCCESSFULLY!")
        print("TJA chart generation model has been trained!")
        print("Check TensorBoard logs for detailed training metrics:")
        print(f"   tensorboard --logdir {summary['configuration']['paths']['tensorboard_logs']}")
        print("=" * 80)

        # Check if ready for Phase 5
        if summary['statistics']['training_success']:
            print("\nReady to proceed to Phase 5: Model Evaluation & Testing")
        else:
            print("\nTraining issues detected - review logs before proceeding")
        
        return 0
        
    except KeyboardInterrupt:
        print("\nPhase 4 training interrupted by user")
        print("Checkpoint should be saved automatically")
        return 1

    except Exception as e:
        print(f"\nPhase 4 training failed: {str(e)}")
        print("\nFull error traceback:")
        traceback.print_exc()

        print("\nTroubleshooting suggestions:")
        print("1. Check GPU memory usage (should be < 7GB)")
        print("2. Verify all Phase 1-3 outputs are present")
        print("3. Check disk space for model checkpoints")
        print("4. Review training logs for detailed error information")
        
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
